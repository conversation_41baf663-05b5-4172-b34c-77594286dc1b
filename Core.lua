--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

local RUI = LibStub('AceAddon-3.0'):NewAddon('RetailUI', 'AceConsole-3.0')
local AceConfig = LibStub("AceConfig-3.0")
local AceDB = LibStub("AceDB-3.0")
RetailUIDB = RetailUIDB or {}
if RetailUIDB.bagsExpanded == nil then
    RetailUIDB.bagsExpanded = false -- Standard: sichtbar
end

RUI.InterfaceVersion = select(4, GetBuildInfo())
RUI.Wrath = (RUI.InterfaceVersion >= 30300)
RUI.DB = nil

-- Function to snap coordinates to grid (centered on screen)
local function SnapToGrid(x, y)
    local GRID_SIZE = RUI.DB.profile.gridSize or 32
    local screenWidth = GetScreenWidth()
    local screenHeight = GetScreenHeight()
    local centerX = screenWidth / 2
    local centerY = screenHeight / 2

    -- Calculate offset from center to nearest grid point
    local offsetX = (x - centerX) % GRID_SIZE
    local offsetY = (y - centerY) % GRID_SIZE

    -- Snap to nearest grid point
    local snappedX = x - offsetX
    local snappedY = y - offsetY

    -- If closer to next grid point, snap to that instead
    if offsetX > GRID_SIZE/2 then
        snappedX = snappedX + GRID_SIZE
    end
    if offsetY > GRID_SIZE/2 then
        snappedY = snappedY + GRID_SIZE
    end

    return snappedX, snappedY
end

-- Table to store center dots for frames
local centerDots = {}

-- Function to create a center dot for a frame
local function CreateCenterDot(frame)
    if not frame or centerDots[frame] then return end

    local dot = frame:CreateTexture(nil, "OVERLAY")

    -- Use configurable size and color
    local dotSize = RUI.DB.profile.centerDotSize or 6
    local dotColor = RUI.DB.profile.centerDotColor or {1, 0, 0, 0.9}

    dot:SetSize(dotSize, dotSize)
    dot:SetPoint("CENTER", frame, "CENTER", 0, 0)
    dot:SetColorTexture(dotColor[1], dotColor[2], dotColor[3], dotColor[4])
    dot:SetDrawLayer("OVERLAY", 7) -- High draw layer to ensure visibility

    centerDots[frame] = dot
    return dot
end

-- Function to remove center dot from a frame
local function RemoveCenterDot(frame)
    if not frame or not centerDots[frame] then return end

    centerDots[frame]:Hide()
    centerDots[frame] = nil
end

-- Function to show center dots for all frames
function RUI:ShowCenterDots()
    -- Check if center dots are enabled in settings
    if not self.DB.profile.showCenterDots then return end

    -- Get all modules that have frames
    local modules = {
        self:GetModule("UnitFrame"),
        self:GetModule("CastingBar"),
        self:GetModule("ActionBar"),
        self:GetModule("Minimap"),
        self:GetModule("QuestTracker"),
        self:GetModule("BuffFrame")
    }

    for _, module in pairs(modules) do
        if module and module.ShowEditorTest then
            -- Get frames from each module (this will need to be customized per module)
            if module.playerFrame then CreateCenterDot(module.playerFrame) end
            if module.targetFrame then CreateCenterDot(module.targetFrame) end
            if module.focusFrame then CreateCenterDot(module.focusFrame) end
            if module.petFrame then CreateCenterDot(module.petFrame) end
            if module.targetOfTargetFrame then CreateCenterDot(module.targetOfTargetFrame) end
            if module.minimapFrame then CreateCenterDot(module.minimapFrame) end
            if module.questTrackerFrame then CreateCenterDot(module.questTrackerFrame) end
            if module.buffFrame then CreateCenterDot(module.buffFrame) end
            if module.castingBarFrame then CreateCenterDot(module.castingBarFrame) end
            if module.bossFrames then
                for _, bossFrame in pairs(module.bossFrames) do
                    CreateCenterDot(bossFrame)
                end
            end
        end
    end
end

-- Function to hide all center dots
function RUI:HideCenterDots()
    for frame, dot in pairs(centerDots) do
        if dot then
            dot:Hide()
        end
    end
    -- Clear the table
    centerDots = {}
end

-- Function to snap a frame to grid by its center point
local function SnapFrameToGridByCenter(frame)
    if not frame then return end

    -- Check if snapping is enabled
    if RUI.DB.profile.enableSnapping == false then return end

    -- Get frame's current center position
    local centerX, centerY = frame:GetCenter()
    if not centerX or not centerY then return end

    -- Check if center-based snapping is enabled
    if RUI.DB.profile.centerBasedSnapping == false then
        -- Use regular snapping instead (snap by anchor point)
        local point, relativeTo, relativePoint, xOfs, yOfs = frame:GetPoint()
        if point and xOfs and yOfs then
            local snappedX, snappedY = SnapToGrid(xOfs, yOfs)
            frame:ClearAllPoints()
            frame:SetPoint(point, relativeTo, relativePoint, snappedX, snappedY)
        end
        return
    end

    -- Snap the center position to grid
    local snappedCenterX, snappedCenterY = SnapToGrid(centerX, centerY)

    -- Calculate the offset needed to move the center to the snapped position
    local offsetX = snappedCenterX - centerX
    local offsetY = snappedCenterY - centerY

    -- Get current anchor point and position
    local point, relativeTo, relativePoint, xOfs, yOfs = frame:GetPoint()
    if point and xOfs and yOfs then
        -- Apply the offset to the current anchor position
        local newX = xOfs + offsetX
        local newY = yOfs + offsetY

        -- Reposition the frame
        frame:ClearAllPoints()
        frame:SetPoint(point, relativeTo, relativePoint, newX, newY)
    end
end

-- Function to check if editor mode is active
local function IsEditorModeActive()
    local editorModule = RUI:GetModule("EditorMode", true)
    return editorModule and editorModule:IsShown()
end

function RUI:OnInitialize()
	RUI.DB = AceDB:New("RetailUIDB", RUI.default, true)

	-- Register options tables
	AceConfig:RegisterOptionsTable("RetailUI", RUI.options)
	AceConfig:RegisterOptionsTable("RUI Commands", RUI.optionsSlash, "rui")

	-- Add to Blizzard Interface Options
	local AceConfigDialog = LibStub("AceConfigDialog-3.0")
	AceConfigDialog:AddToBlizOptions("RetailUI", "RetailUI")
end

function RUI:OnEnable()
    if GetCVar("useUiScale") == "0" then
        SetCVar("useUiScale", 1)
        SetCVar("uiScale", 0.75)
    end

    -- Initialize minimap button
    self:InitializeMinimapButton()
end

function RUI:OnDisable() end

function CreateUIFrame(width, height, frameName)
	local frame = CreateFrame("Frame", 'RUI_' .. frameName, UIParent)
	frame:SetSize(width, height)

	frame:RegisterForDrag("LeftButton")
	frame:EnableMouse(false)
	frame:SetMovable(false)
	frame:SetScript("OnDragStart", function(self, button)
		self:StartMoving()
	end)
	frame:SetScript("OnDragStop", function(self)
		self:StopMovingOrSizing()

		-- Apply center-based grid snapping if editor mode is active
		if IsEditorModeActive() then
			SnapFrameToGridByCenter(self)
		end
	end)

	frame:SetFrameLevel(100)
	frame:SetFrameStrata('FULLSCREEN')

	do
		local texture = frame:CreateTexture(nil, 'BACKGROUND')
		texture:SetAllPoints(frame)
		texture:SetTexture("Interface\\AddOns\\RetailUI\\Textures\\UI\\ActionBarHorizontal.blp")
		texture:SetTexCoord(0, 512 / 512, 14 / 2048, 85 / 2048)
		texture:Hide()

		frame.editorTexture = texture
	end

	do
		local fontString = frame:CreateFontString(nil, "BORDER", 'GameFontNormal')
		fontString:SetAllPoints(frame)
		fontString:SetText(frameName)
		fontString:Hide()

		frame.editorText = fontString
	end

	return frame
end

RUI.frames = {}

function ShowUIFrame(frame)
	frame:SetMovable(false)
	frame:EnableMouse(false)

	frame.editorTexture:Hide()
	frame.editorText:Hide()

	for _, target in pairs(RUI.frames[frame]) do
		target:SetAlpha(1)
	end

	RUI.frames[frame] = nil
end

function HideUIFrame(frame, exclude)
	frame:SetMovable(true)
	frame:EnableMouse(true)

	frame.editorTexture:Show()
	frame.editorText:Show()

	RUI.frames[frame] = {}

	exclude = exclude or {}

	for _, target in pairs(exclude) do
		target:SetAlpha(0)
		tinsert(RUI.frames[frame], target)
	end
end

function SaveUIFramePosition(frame, widgetName)
	-- Get the current anchor point (not center) for consistent saving
	local point, _, relativePoint, posX, posY = frame:GetPoint()

	-- Apply center-based grid snapping when saving position
	if posX and posY then
		-- First snap the frame by center to ensure it's properly aligned
		SnapFrameToGridByCenter(frame)
		-- Then get the updated position after snapping
		point, _, relativePoint, posX, posY = frame:GetPoint()
	end

	RUI.DB.profile.widgets[widgetName].anchor = relativePoint or point
	RUI.DB.profile.widgets[widgetName].posX = posX
	RUI.DB.profile.widgets[widgetName].posY = posY
end

function SaveUIFrameScale(input, widgetName)
    local scale = tonumber(input) -- Convert input to a number
	if not scale or scale <= 0 then -- validate
		print("Invalid scale. Please provide a positive number.")
		return
	end

	RUI.DB.profile.widgets[widgetName].scale = scale -- save the scale

    local UnitFrameModule = RUI:GetModule("UnitFrame") -- update the UI to reflect the changes
    UnitFrameModule:UpdateWidgets()
    print(widgetName .. " Frame Scale saved as " .. GetUIFrameScale(widgetName)) -- print confirmation to a user
end

function GetUIFrameScale(widgetName)
	return RUI.DB.profile.widgets[widgetName].scale
end

function CheckSettingsExists(self, widgets)
	for _, widget in pairs(widgets) do
		if RUI.DB.profile.widgets[widget] == nil then
			self:LoadDefaultSettings()
			break
		end
	end
	self:UpdateWidgets()
end

local function MoveChatOnFirstLoad()
    local chat = ChatFrame1
    if not chat then return end

    if chat:IsUserPlaced() then return end

    chat:ClearAllPoints()
    chat:SetPoint("BOTTOMLEFT", UIParent, "BOTTOMLEFT", 32, 32)
    chat:SetWidth(chat:GetWidth() - 40)
    chat:SetMovable(true)
    chat:SetUserPlaced(true)
end

local f = CreateFrame("Frame")
f:RegisterEvent("PLAYER_ENTERING_WORLD")
f:SetScript("OnEvent", function(self, event)
    MoveChatOnFirstLoad()
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
end)
